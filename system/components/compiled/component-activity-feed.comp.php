<?php namespace edgeTemplate\component_activity_feed;use edge\Edge;use edge\loop;use function icons\icon_micro;?>
<?php extract(Edge::process_props(['title' => 'activity_feed',
    'description' => 'displays a list of stuff',
    'activity' => [],
    'currentUser' => ''
],$edge_data)); ?><?php require_once(FS_CLASSES . 'edge_loop.class.php');$loop = new loop(0);?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>
<?php
    print_rr($activity,'activityey',true,true);
?>
<ul role="list" class="space-y-6">
    <?php if (is_countable($activity)) { $loop = new loop(count($activity),$loop->depth+1,$loop); foreach($activity as $index => $activityItem): ?>
        <ul role="list" class="space-y-6">
            <li class="relative flex gap-x-4">
                <div class="absolute -bottom-6 left-0 top-0 flex w-6 justify-center">
                    <div class="w-px bg-gray-200"></div>
                </div>

                <div class="relative flex size-6 flex-none items-center justify-center bg-white text-gray-300">

                        <?php if($activityItem['hist_media'] == 'E-Mail'): ?>
                            <?= icon_micro('envelope') ?>
                        <?php elseif($activityItem['hist_media'] == 'phone'): ?>
                            <?= icon_micro('phone') ?>
                        <?php elseif($activityItem['hist_media'] == 'In-Person'): ?>
                            <?= icon_micro('user') ?>
                        <?php elseif($activityItem['hist_media'] == 'Letter'): ?>
                            <?= icon_micro('inbox_icon_down') ?>
                        <?php else: ?>
                        <div class="size-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300 text-gray-300  items-center"></div>
                        <?php endif ?>

                </div>
                <p class="flex-auto py-0.5 text-xs/5 text-gray-500"><span class="mr-2 font-medium text-gray-900"><?= $activityItem['users_name'] ?></span><?= $activityItem['hist_message'] ?></p>
                <time datetime="<?= $activityItem['hist_date'] ?>" class="flex-none py-0.5 text-xs/5 text-gray-500"><?= $activityItem['hist_date'] ?></time>
            </li>

        </ul>
    <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
</ul>
<?php } ?>