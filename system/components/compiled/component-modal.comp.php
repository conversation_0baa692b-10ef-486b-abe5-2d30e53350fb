<?php namespace edgeTemplate\component_modal;use edge\Edge;?><?php extract(Edge::process_props(['title' => 'model',
    'description' => 'A model',
    'content' => [], 
    'title' => '',
],$edge_data));;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>

<div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-show="showModal" style="display:none;">
    <div class="bg-opacity-75 fixed inset-0 bg-gray-500 transition-opacity" aria-hidden="true"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"></div>
    <div class="w-screen overflow-y-auto fixed inset-0 z-50">
        <div class="min-h-full flex justify-center items-end p-4 text-center sm:items-center sm:p-0">
            <div id="modal" class="transform  relative text-left bg-white rounded-lg shadow-xl transition-all sm:m-8xl sm:my-8"
                 @click.away="showModal = false" x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100">
                <div class="">
                    <div class="sm:flex sm:items-start">
                        <div id="modal_body" class="w-full m-0 text-center sm:mt-0 sm:ml-0 sm:text-left"></div>
                    </div>
                </div>
                
                
                
                
            </div>
        </div>
    </div>
</div>

<?php } ?>