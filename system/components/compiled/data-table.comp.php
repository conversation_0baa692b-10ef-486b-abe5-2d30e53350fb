<?php namespace edgeTemplate\data_table;use edge\Edge;use edge\loop;?><?php extract(Edge::process_props(['title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'items' => [], 
    'id_count' => edge::id_count(),
    'columns' => [[
            'label' => 'Name',
            'field' => 'name',
            'filter' => false,
            'extra_parameters' => ''
     ]], 
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],
    'just_body' => false, 
    'just_rows' => false, 
    'items_per_page' => 30, 
    'current_page_num' => 1,
    'class' => '', 
    'sort_column' => '',
    'sort_direction' => '',
    'auto_update' => false,
    'callback' => null,
    ''
],$edge_data));$loop = new Loop(0);print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>
<?php print_rr($columns,'columns poo');
print_rr($callback,'callybackA');
?>
<?php if(!$just_body && !$just_rows): ?>

<?php if($auto_update): ?>
    <?= Edge::render('forms-input', ["type" => "hidden", "name" => "last_update", 'value' => date("Y-m-d H:i:s"), "hx-post" => "api/update", "hx-swap" => "outerHTML", "hx-trigger" => "every 10s"], 1) ?>
<?php endif ?>
    <input type="hidden" class="data_table_filter" name="callback" value="<?= $callback ?>">
    <table class="min-w-full border-collapse search_target data_table <?= $class ?>" >
        <thead>
            <tr>
                <?php if (is_countable($columns)) { $loop = new loop(count($columns),$loop->depth+1,$loop); foreach($columns as $col): ?>
                    <th scope="col" class="<?= $loop->first ? 'relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8' : 'relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell' ?>" style="isolation: isolate;">
                        <?= Edge::render('data-table-filter', ['label' => $col['label'], 'col' => $col, 'sort_col' => $sort_column, 'sort_dir' => $sort_direction, 'callback' => $callback, 'id_count' => $id_count], 1) ?><?= Edge::render('data-table-filter',null,2) ?>
                    </th>
                <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
            </tr>
        </thead>
<?php endif ?> 
<?php if(!$just_rows): ?>
<tbody class="bg-white data_table_body">
<?php endif ?> 
    <?php if (is_countable($items)) { $loop = new loop(count($items),$loop->depth+1,$loop); foreach($items as $item): ?>
        <?php if($loop->first): ?>
           <?= print_rr($item,'itamage') ?>
        <?php endif ?>
        <tr class="border-t <?= $loop->first ? 'border-gray-300' : 'border-gray-200' ?> <?= $rows['class_postfix'] ?>" id="<?= $rows['id_prefix'] . $item[$rows['id_field']] . $rows['id_postfix'] ?>" <?= $rows['extra_parameters'] ?>>
            <?php if (is_countable($columns)) { $loop = new loop(count($columns),$loop->depth+1,$loop); foreach($columns as $col): ?>
                <?php if($col['replacements']): ?>
                    <?php $item[$col['field']] = str_replace(array_keys($col['replacements']),$col['replacements'],$item[$col['field']]) ?>
                <?php endif ?>
                <td class="<?= $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell' ?>">
                    <?php if(isset($col['content'])): ?>
                        <?php if(is_callable($col['content'])): ?>
                            <?= $col['content']($item,$loop->first) ?>
                        <?php else: ?>
                            <?= $col['content'] ?? 'content' ?>
                        <?php endif ?>
                    <?php elseif(isset($col['field'])): ?>
                        <?php if(is_array($col['field'])): ?>
                            <?= implode('<br>', array_map(fn($f) => $item[$f] ?? '', $col['field'])) ?>
                        <?php elseif(is_string($col['field'])): ?>
                            <?= $item[$col['field']] ?? '' ?>
                        <?php endif ?>
                    <?php endif ?>
                </td>
            <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
        </tr>
        <?php if($items_per_page > 1 && $loop->iteration > $items_per_page ): ?>
            <?php break; ?>
        <?php endif ?>
    <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
<?php if(!$just_rows): ?>
</tbody>
<?php endif ?>
<?php if(!$just_body && !$just_rows): ?>
        <tfoot>
            <tr>
                <td colspan="<?= count($columns) ?>">
                    <?= Edge::render('pagination-strip', ['item_count' => count($items), 'items_per_page' => $items_per_page, 'current_page' => $current_page], 1) ?><?= Edge::render('pagination-strip',null,2) ?>
                </td>
            </tr>
        </tfoot>
    </table>
<?php endif ?>
<?php } ?>