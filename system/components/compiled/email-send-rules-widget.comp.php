<?php namespace edgeTemplate\email_send_rules_widget;use edge\Edge;use edge\loop;?><?php extract(Edge::process_props(['rules' => [],
    'first_class' => 'relative inline-flex items-center rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10',
    'main_class' => 'relative -ml-px inline-flex items-center bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10',
    'last_class' => 'relative -ml-px inline-flex items-center rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10'
],$edge_data)); ?><?php require_once(FS_CLASSES . 'edge_loop.class.php');$loop = new loop(0);?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>

<nav id="rules_nav" 
    class="-space-x-px isolate inline-flex rounded-md shadow-sm" 
    x-data="{ 
        editing: false, 
        inputVisible: false,
        saveRule() {
            this.inputVisible = false;
            this.editing = false;
        },
        triggerDelete(event) {
            if (this.editing) {
                htmx.trigger(event.target, 'delete');
            }
        }
    }">
    
    <div class="relative flex items-center" id="rules_buttons">
        <?php if (is_countable($rules)) { $loop = new loop(count($rules),$loop->depth+1,$loop); foreach($rules as $key => $rule): ?>
            <button 
                class="<?= $key === 0 ? $first_class : $main_class ?>"
                @click="triggerDelete($event)"
                hx-trigger="delete"
                hx-post="<?= APP_ROOT ?>api/email_history"
                hx-target="#rules_buttons"
                hx-vals='{"action": "email_rule_delete", "rule": "<?= $rule ?>"}'
                hx-swap="outerHTML">
                <?= $rule ?>
            </button>
        <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
    </div>

    <div class="relative flex items-center" x-show="inputVisible" x-transition>
        <?= Edge::render('forms-input', ["name" => "new_rule", "type" => "text", "id" => "new_rule", "class" => "block w-full border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6", "placeholder" => "Edit Rule"]) ?>
    </div>

    <button x-show="editing"
        class="<?= $last_class ?>"
        hx-post="<?= APP_ROOT ?>api/email_history"
        hx-include="#new_rule"
        hx-target="#rules_buttons"
        hx-swap="outerHTML"
        hx-vals='{"action": "email_rule_add"}'>
        +
    </button>

    <button x-show="editing"
        @click="saveRule"
        x-transition
        class="<?= $last_class ?>">
        save
    </button>

    <button x-show="!editing"
        @click="editing = true; inputVisible = true;"
        x-transition
        class="<?= $last_class ?>">
        edit
    </button>
</nav><?php } ?>