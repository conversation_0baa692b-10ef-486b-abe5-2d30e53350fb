<?php namespace edgeTemplate\forms_button;use edge\Edge;use function icons\icon;?><?php extract(Edge::process_props(['label' => '',
    'variant' => 'primary', 
    'size' => 'md', 
    'icon_position' => null, 
    'icon' => null,
    'edge-icon' => null,
    'class' => '',
    'class_suffix' => ''
],$edge_data));;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>

<?php
    $baseClasses = 'inline-flex items-center font-semibold ';
    $sizeClasses = match($size) {
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-2 py-1 text-sm',
        'lg' => 'px-3 py-2 text-sm',
        'xl' => 'px-3.5 py-2.5 text-sm',
        default => 'px-2.5 py-1.5 text-sm',
    };
    $variantClasses = match($variant) {
        'primary' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600',
        'secondary' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50',
        'dark-primary' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-md bg-indigo-500 text-white hover:bg-indigo-400 focus-visible:outline-indigo-500',
        'dark-secondary' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-md bg-white/10 text-white hover:bg-white/20',
        'soft' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-md bg-indigo-50 text-indigo-600 hover:bg-indigo-100',
        'rounded-primary' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-full bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600',
        'rounded-secondary' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-full bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50',
        'circular' => 'shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 rounded-full bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600 px-1 py-1',
        'blank' => 'outline-none bg-transparent text-gray-900 hover:bg-gray-50',
         default => 'rounded-md bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600',
    };
    $iconClasses = '-ml-0.5 size-5';
    $trailingIconClasses = '-mr-0.5 size-5';

?>

<button class = "<?= Edge::merge_tailwind_classes($baseClasses, $sizeClasses, $variantClasses, $class, $class_suffix) ?>" <?= $extra_attributes ?>>
    <?php if($icon_position === 'leading' && $icon): ?>
        <span class="<?= $iconClasses ?>">
            <?= icon($icon) ?>
        </span>
    <?php endif ?>
    <span>
        <?php if($icon_position == 'replace' || ($icon && $label == '') ): ?>
            <?= icon($icon) ?>
        <?php else: ?>
            <?= $label ?>
        <?php endif ?>
    </span>
    <?php if($icon_position === 'trailing' && $icon): ?>
        <span class="<?= $trailingIconClasses ?>">
            <?= icon($icon) ?>
        </span>
    <?php endif ?>
    <?php } if($tag_content == 0 || $tag_content == 2 ){ ?>
<?php } ?></button>