<?php namespace edgeTemplate\forms_input_select;use edge\Edge;;?><?php extract(Edge::process_props(['title' => 'Select',
'description' => 'Basic form select',
'data' => [], 
'label' => '',
'name' => '',
'class_suffix' => '',
'options' => [], 
'selected' => null, 
],$edge_data)); ?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>

<div>
  <?php if($label): ?>
    <label for="email" class="block text-sm/6 font-medium text-gray-900"><?= $label ?></label>
  <?php endif ?>
  <div class="mt-2">
    <div class="flex items-center rounded-md bg-white pl-3 outline outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600">
      <input type="<?= $type ?? 'text' ?>"
             name="<?= $name ?>"
             id="<?= $id ?>"
             class="block min-w-0 grow py-1.5 pl-1 pr-3 text-base text-gray-900 placeholder:text-gray-400 focus:outline focus:outline-0 sm:text-sm/6" placeholder="">
      <div class="grid shrink-0 grid-cols-1 focus-within:relative">
        <?= Edge::render('forms-select-invisible', ['id' => '' . $id ?? 'select' . '', 'name' => '' . $name ?? 'select' . '', 'class' => 'col-start-1 row-start-1 w-full appearance-none rounded-md py-1.5 pl-3 pr-7 text-base text-gray-500 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6', 'options' => $options]) ?>
      </div>
    </div>
  </div>
</div>
<?php } ?>