<?php namespace edgeTemplate\forms_select_invisible;use edge\Edge;use edge\loop;?><?php extract(Edge::process_props(['title' => 'Select',
'description' => 'Basic form select',
'data' => [], 
'label' => '',
'name' => '',
'class' => '',
'class_suffix' => '',
'options' => [], 
'selected' => null, 
],$edge_data)); ?><?php require_once(FS_CLASSES . 'edge_loop.class.php');$loop = new loop(0);?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>
<div>
  <div class="m-0 grid grid-cols-1">
    <select id="<?= $id ?? 'select' ?>" name="<?= $name ?? 'select' ?>" <?= $extra_attributes ?> class="<?= $class ?? "col-start-1 row-start-1 w-full appearance-none rounded-md py-0 pr-6 pl-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 sm:text-sm/" ?> <?= $class_suffix ?? '' ?>">
      <option value="" <?= ($selected ?? null) == $label ? 'selected' : '' ?>><?= $label ?></option>
      <?php if (is_countable($options ?? [])) { $loop = new loop(count($options ?? []),$loop->depth+1,$loop); foreach($options ?? [] as $key => $value): ?>
        <option value="<?= $key ?>" <?= ($selected ?? null) == $key ? 'selected' : '' ?>><?= $value ?></option>
      <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
    </select>
     </div>
</div><?php } ?>