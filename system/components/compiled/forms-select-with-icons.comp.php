<?php namespace edgeTemplate\forms_select_with_icons;use edge\Edge;use edge\loop;use function icons\icon;?><?php extract(Edge::process_props(['title' => 'Select with Icons',
    'description' => 'Dropdown select with icons',
    'name' => '',
    'label' => 'Select an option',
    'options' => [], 
    'selected' => null, 
    'class' => '',
],$edge_data));$loop = new Loop(0);print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>
<?php
if (!isset($options[0]['id'])) {
    $options = array_map(function($option) {
        return [
            'id' => $option,
            'name' => $option,
            'icon' => icon($option)
        ];
    }, $options);
}
?>
<div x-data='{
    open: false,
    hover: false,
    selectedOption: <?= json_encode($selected ? $selected : (count($options) > 0 ? $options[0] : null)) ?>,
    selectOption(option) {
        this.selectedOption = option;
        this.open = false;
    }
}' class="relative">
    <?php if($label): ?>
        <label for="<?= $name ?>" class="block text-sm/6 font-medium text-gray-900"><?= $label ?></label>
    <?php endif ?>
    
    <div class="relative mt-2">
        <button type="button"
            @click="open = !open"
            @keydown.escape.window="open = false"
            @click.away="open = false"
            class="grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 <?= $class ?>">
            <span class="col-start-1 row-start-1 flex items-center gap-3 pr-6">
                <span class="size-5 shrink-0 text-gray-400" x-html="selectedOption ? selectedOption.icon : 'question-mark-circle'" x->
                </span>
                <span class="block truncate" x-text="selectedOption ? selectedOption.name : 'Select an option'"></span>
            </span>
            <span class="col-start-1 row-start-1 self-center justify-self-end text-gray-500">
                <?= icon('chevron-up-down') ?>
            </span>
        </button>

        <input type="hidden" name="<?= $name ?>" x-bind:value="selectedOption ? selectedOption.id : ''">

        <div x-show="open" 
             x-transition:leave="transition ease-in duration-100"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden sm:text-sm">
            <ul class="">
                <?php if (is_countable($options)) { $loop = new loop(count($options),$loop->depth+1,$loop); foreach($options as $option): ?>
                    <li @click='selectOption(<?= json_encode($option) ?>)'
                        :class="[selectedOption && selectedOption.id === '<?= $option['id'] ?>' ? 'bg-indigo-500 text-white' : '', 'relative cursor-default pt-1 pb-1 pr-9 pl-3 hover:bg-indigo-600 hover:text-white']">
                        <div class="flex items-center">
                            <span class="size-6 shrink-0 text-gray-400" :class="{'text-white': selectedOption && selectedOption.id === '<?= $option['id'] ?>' && open}">
                                <?= icon($option['id']) ?>
                            </span>
                            <span class="ml-6 block truncate">
                                <?= $option['name'] ?>
                            </span>
                        </div>

                        <span x-show="selectedOption && selectedOption.id === '<?= $option['id'] ?>'" 
                              :class="{'text-white': open && hover, 'text-indigo-600': !(open && hover)}"
                              class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <?= icon('check') ?>
                        </span>
                    </li>
                <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
            </ul>
        </div>
    </div>
</div><?php } ?>