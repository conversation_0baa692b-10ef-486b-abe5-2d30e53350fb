<?php namespace edgeTemplate\forms_textarea;use edge\Edge;?><?php extract(Edge::process_props(['title' => 'form textarea control',
    'description' => 'Basic form select',
    'content' => '', 
    'label' => null,
    'id' => '',
    'name' => '',
    'rows' => 4,
    'placeholder' => '',
    'class_suffix' => '',
],$edge_data));;print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>
<div>
    <?php if($label): ?>
        <label for="<?= $name ?>" class="block text-sm/6 font-medium text-gray-900"><?= $label ?></label>
    <?php endif ?>
    <textarea
            rows="<?= $rows ?>"
            name="<?= $name ?>"
            id="<?= $id ?>"
            class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 <?= $class_suffix ?>"
            <?php if($placeholder): ?> placeholder="<?= $placeholder ?>" <?php endif ?>
            <?= $extra_attributes ?>
    ><?= $content ?></textarea>
</div>

<?php } ?>