<?php namespace edgeTemplate\html_div;use edge\Edge;;?><?php extract(Edge::process_props(['title' => 'html div',
    'description' => 'A html div',
    'class_suffix' => '',
],$edge_data)); ?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>
<div class="<?= $class_suffix ?>">
        <?php } if($tag_content == 0 || $tag_content == 2 ){ ?>
</div><?php } ?>