<?php namespace edgeTemplate\nav_tree;use edge\Edge;use edge\loop;use const icons\ICONS;?><?php extract(Edge::process_props(['items' => [],
    'parent_path' => '',
    'depth' => 0,
],$edge_data));$loop = new Loop(0);print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>


<ul
    @switch ($depth)
        @case(0) x-data="initNavTree()" x-init="init()"
        @default class="bg-indigo-<?= $depth+6 ?>00 shadow-inner shadow-indigo-<?= $depth+7 ?>00"
    @endswitch
>
    <?php if (is_countable($items)) { $loop = new loop(count($items),$loop->depth+1,$loop); foreach($items as $item): ?>
        <?php if( count($item['required_roles']) > 0 && !in_array(USER_ROLE, $item['required_roles']) ): ?> <?php continue; ?> <?php endif ?>
        <li :class='collapsed ? "mx-0" : "mx-0"'>
            <?php
                $item_route = trim($parent_path . $item['route_key'], '/');
            ?>
            <a href='<?= $parent_path ?><?= $item['route_key'] ?>'
               hx-get='<?= APP_ROOT ?><?= $parent_path ?><?= $item['route_key'] ?>'
               hx-replace-url='<?= APP_ROOT ?><?= $parent_path ?><?= $item['route_key'] ?>'
               hx-target='#content_wrapper' <?= $folder_out ?>
               class='flex gap-x-3 p-2 text-sm font-semibold leading-6 group hover:text-white inset-shadow-sm'
               :class="currentRoute === '<?= $item_route ?>' || (currentRoute.startsWith('<?= $item_route ?>/') && <?= isset($item['sub_folder']) ? 'true' : 'false' ?>) ? 'border-y-2 border-y-green-600 text-white bg-green-700' : 'text-indigo-200  hover:bg-indigo-<?= $depth + 6 + 1 ?>00'"
               x-state:on='Current' x-state:off='Default'
               x-state="currentRoute === '<?= $item_route ?>' || (currentRoute.startsWith('<?= $item_route ?>/') && <?= isset($item['sub_folder']) ? 'true' : 'false' ?>) ? 'on' : 'off'"
               @click="currentRoute = '<?= $item_route ?>'; localStorage.setItem('currentNavRoute', '<?= $item_route ?>'); window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: '<?= $item_route ?>' } }));"
               x-state-description='Current: "border-y-2 border-y-green-600 text-white bg-green-700", Default: "border-y-2 border-y-indigo-<?= $depth+6 ?>00 text-indigo-200 hover:text-white hover:bg-indigo-700"'>
                <span class='shrink-0'><?= ICONS[$item['icon']] ?></span>
                <span class='transition-opacity duration-300 whitespace-nowrap' :class='collapsed ? "opacity-0" : "opacity-100"'>
                    <?= $item['name'] ?>
                </span>
            </a>
            <?php if(isset($item['sub_folder'])): ?>
                 <?= Edge::render('nav-tree', ['items' => $item['sub_folder'], 'parent_path' => $parent_path . $item['route_key'] . '/', 'depth' => $depth + 1]) ?>
            <?php endif ?>
        </li>
    <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>

    <?php if(in_array(USER_ROLE, ['admin', 'dev'])): ?>
        <li :class='collapsed ? "mx-1" : "mx-2"' class="mt-2">
            <button
                hx-post="<?= APP_ROOT ?>api/nav_tree/add_nav_entry"
                hx-vals='{"parent_path": "<?= $parent_path ?>"}'
                hx-target="#modal_body"
                @click='showModal = true'
                class="flex gap-x-3 p-2 text-sm font-semibold leading-6 text-green-400 rounded-md group hover:text-white hover:bg-green-700 w-full"
            >
                <span class="shrink-0"><?= ICONS['plus'] ?></span>
                <span class="transition-opacity duration-300 whitespace-nowrap" :class='collapsed ? "opacity-0" : "opacity-100"'>
                    Add Entry
                </span>
            </button>
        </li>
    <?php endif ?>
</ul><?php } ?>