<?php namespace edgeTemplate\pagination_strip;use edge\Edge;use edge\loop;use edge\pagination;?><?php extract(Edge::process_props(['title' => 'pagination strip',
    'description' => 'displays pagination stats and buttons',
    'first_item' => 0, 
    'item_count' => 1, 
    'items_per_page' => 30, 
    'current_page_num' => 1
],$edge_data));$loop = new Loop(0);$pagination = new pagination($item_count,$first_item,$current_page_num,$items_per_page);print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){?>


<div class="flex bottom-0 items-center justify-between border-t border-gray-200 bg-white px-4 py-2 sm:px-6">
    <div class="flex flex-1 justify-between sm:hidden">
        <a href="#" 
           hx-get="api/data_table/pagination"
           hx-target="#data_table_container"
           hx-include="[name='search_terms'], [name='per_page']"
           hx-vals='{"page": <?= max(1, $pagination->current_page - 1) ?>}'
           class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
        <a href="#"
           hx-get="api/data_table/pagination"
           hx-target="#data_table_container"
           hx-include="[name='search_terms'], [name='per_page']"
           hx-vals='{"page": <?= min($pagination->total_pages, $pagination->current_page + 1) ?>}'
           class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
    </div>
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium"><?= $pagination->first_item ?></span>
                to
                <span class="font-medium"><?= $pagination->last_item ?></span>
                of
                <span class="font-medium"><?= $pagination->item_count ?> </span>
                results
            </p>
        </div>
        <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <a href="#"
                   hx-get="api/data_table/pagination"
                   hx-target="#data_table_container"
                   hx-include="[name='search_terms'], [name='per_page']"
                   hx-vals='{"page": <?= max(1, $pagination->current_page - 1) ?>}'
                   class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                    <span class="sr-only">Previous</span>
                    <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                        <path fill-rule="evenodd"
                              d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z"
                              clip-rule="evenodd"/>
                    </svg>
                </a>
                
                <?php if (is_countable($pagination->page_array())) { $loop = new loop(count($pagination->page_array()),$loop->depth+1,$loop); foreach($pagination->page_array() as $page): ?>
                    <a href="#"
                       hx-get="api/data_table/pagination"
                       hx-target="#data_table_container"
                       hx-include="[name='search_terms'], [name='per_page']"
                       hx-vals='{"page": <?= $page ?>}'
                       <?= $pagination->current_page == $page ? 'aria-current="page" class="relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"' : 'class="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex"' ?>>
                       <?= $page ?>
                    </a>
                <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>

                
                <a href="#"
                   hx-get="api/data_table/pagination"
                   hx-target="#data_table_container"
                   hx-include="[name='search_terms'], [name='per_page']"
                   hx-vals='{"page": <?= min($pagination->total_pages, $pagination->current_page + 1) ?>}'
                   class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                <span class="sr-only">Next</span>
                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd"
                          d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                          clip-rule="evenodd"/>
                </svg>
                </a>
            </nav>
        </div>
    </div>
</div><?php } ?>