<?php namespace edgeTemplate\subscription_table_config_filter_row;use edge\Edge;;?><?php extract(Edge::process_props(['columnIndex',
    'filterKey' => '',
    'filterValue' => '',
    'is_new' => false
],$edge_data)); ?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>

<div class="filter-item flex gap-2">
    <input type="text" name="columns[<?= $columnIndex ?>][filter_keys][]"
           value="<?= $filterKey ?>"
           class="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
           placeholder="Key">
    <input type="text" name="columns[<?= $columnIndex ?>][filter_values][]"
           value="<?= $filterValue ?>"
           class="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
           placeholder="Value">
    <button type="button" class="text-red-600 hover:text-red-800"
            hx-delete="api/remove_filter"
            hx-target="closest .filter-item"
            hx-swap="outerHTML">×
    </button>
</div><?php } ?>