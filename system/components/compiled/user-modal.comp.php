<?php namespace edgeTemplate\user_modal;use edge\Edge;;?><?php extract(Edge::process_props(['user' => null,
    'roles' => ['user' => 'User', 'admin' => 'Admin', 'dev' => 'Developer'],
    'statuses' => ['active' => 'Active', 'inactive' => 'Inactive'],
],$edge_data)); ?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>

<div class="space-y-4 p-5">
    <div class="border-b border-gray-200 pb-4">
        <h3 class="text-lg font-medium leading-6 text-gray-900">
            <?= $user ? 'Edit User' : 'Create New User' ?>
        </h3>
    </div>

    <form hx-post="api/save_user" 
          hx-target="#users_table" 
          hx-swap="innerHTML"
          @submit="showModal = false"
          class="space-y-4">
        
        <input type="hidden" name="id" value="<?= $user ? $user['id'] : '' ?>">
        
        <div>
            <?= Edge::render('forms-input', ["name" => "name", "label" => "Name", 'value' => $user ? $user['name'] : '']) ?>
        </div>
        
        <div>
            <?= Edge::render('forms-input', ["name" => "email", "label" => "Email", 'value' => $user ? $user['email'] : '']) ?>

        </div>
        <div>
           <?= Edge::render('forms-select', ["name" => "role", "label" => "Role", 'options' => $roles, 'selected' => "" . $user ? $user['role'] : null . ""]) ?>
          </div>

        <div>
           <?= Edge::render('forms-select', ["name" => "status", "label" => "Status", 'options' => $statuses, 'selected' => "" . $user ? $user['status'] : null . ""]) ?>

       </div>
        
        <?php if(!$user): ?>
        <div>
            <?= Edge::render('forms-input', ["name" => "password", "label" => "Password"]) ?>
        </div>
        <?php endif ?>

        <div class="flex justify-end gap-x-3 pt-4">
            <?= Edge::render('forms-button', ["label" => "Cancel", "variant" => "secondary", "@click" => "showModal = false"]) ?>
            <?= Edge::render('forms-button', ["type" => "submit", 'label' => $user ? 'Update' : 'Create', "variant" => "primary"]) ?>
        </div>
    </form>
</div>

<div id="password_reset_message" 
     class="fixed top-4 right-4" 
     x-data="{ show: false, message: '' }" 
     @password-reset.window="show = true; message = $event.detail; setTimeout(() => show = false, 3000)"
     x-show="show"
     x-transition>
    <div class="bg-green-50 p-4 rounded-md">
        <p class="text-green-800" x-text="message"></p>
    </div>
</div><?php } ?>