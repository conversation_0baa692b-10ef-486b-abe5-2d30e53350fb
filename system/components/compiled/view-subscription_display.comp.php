<?php namespace edgeTemplate\view_subscription_display;use edge\Edge;use edge\loop;?><?php extract(Edge::process_props(['title' => 'Subscription Display',
    'description' => '',
    'items' => [], 
    'data' => [],
    'histdata' => [],
    'class' => ''
],$edge_data)); ?><?php require_once(FS_CLASSES . 'edge_loop.class.php');$loop = new loop(0);?><?php print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>
<div class="grid grid-cols-3 bg-gray-200">
<?php if (is_countable($data)) { $loop = new loop(count($data),$loop->depth+1,$loop); foreach($data as $card): ?>

    <div class="flex flex-col <?= $card['class_suffix'] ?>">

    <?= Edge::render($card['type'] ?? 'layout-card', ['label' => $card['label'], "collapsible" => "true", 'collapsed' => "" . $card['collapsed'] ?? 'false' . "", "class_suffix" => "", 'internal_class_suffix' => "" . $card['internal_class_suffix'] . ""], 1) ?>

        <?php if (is_countable($card['content'])) { $loop = new loop(count($card['content']),$loop->depth+1,$loop); foreach($card['content'] as $key => $column): ?>
            <?php if($column['type'] == 'component-activity-feed'): ?>
                <?= Edge::render('component-activity-feed', ['activity' => $histdata], 1) ?>
            <?php else: ?>
                <?php if(!isset($column['content'])): ?>
                    <div class="px-1 py-1 h-min sm:grid sm:grid-cols-2 sm:gap-4 sm:px-0">
                        <dt class="text-sm/6 font-medium text-gray-900"><?= $column['label'] ?></dt>
                        <dd class="mt-1 text-sm/6 text-gray-700 sm:mt-0">
                            <?= $column['value'] ?>
                        </dd>
                    </div>
                <?php else: ?>
                    <?= Edge::render($column['type'] ?? 'layout-card', ['label' => $column['label'], "collapsible" => "true", 'collapsed' => "" . $column['collapsed'] ?? 'false' . "", 'class_suffix' => "" . $column['class_suffix'] . "", 'internal_class_suffix' => "" . $column['internal_class_suffix'] . ""], 1) ?>
                        <?php if (is_countable($column['content'])) { $loop = new loop(count($column['content']),$loop->depth+1,$loop); foreach($column['content'] as $subKey => $subColumn): ?>
                            <div class="<?= isset($column['hide_labels']) && $column['hide_labels'] ? 'px-0 py-0' : 'px-1 py-1' ?>  h-min sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt class="text-sm/6 font-medium text-gray-900 <?= isset($column['hide_labels']) && $column['hide_labels'] ? 'hidden' : '' ?>">
                                    <?= $subColumn['label'] ?>
                                </dt>
                                <dd class=" text-sm/6 text-gray-700 <?= isset($column['hide_labels']) && $column['hide_labels'] ? 'mt-0 col-span-3' : 'mt-1 sm:col-span-2' ?> sm:mt-0">
                                    <?= $subColumn['value'] ?>
                                </dd>
                            </div>
                        <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
                    <?= Edge::render($column['type'] ?? 'layout-card',null,2) ?>
                <?php endif ?>
            <?php endif ?>
        <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>

    <?= Edge::render($card['type'] ?? 'layout-card',null,2) ?>
    </div>
<?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
</div><?php } ?>