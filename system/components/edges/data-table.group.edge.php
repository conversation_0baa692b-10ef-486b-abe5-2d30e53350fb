@props([
    'title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'data' => [], // data array of items
    'columns' => [], // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
    'just_body' => false // just return body
])
@if (!$just_body)
                    <table class="min-w-full border-separate border-spacing-0 data_table" >
                        <thead >
                        <tr>
                            <td class="{{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-3 py-4 text-sm text-gray-500 sm:table-cell' }}">
                                #
                            </td>
                            @foreach($columns as $col)

                                <th scope="col" class="sticky top-0 border-b border-gray-300 bg-white/75 py-1 pr-1 pl-1 text-left text-sm font-semibold text-gray-900 backdrop-blur-sm backdrop-filter sm:pl-6 lg:pl-8">
                                    @if ($col['filter'])
                                        <x-forms-select
                                                id="col_{{ $col['field'] }}_filter"
                                                :name="$col['field']"
                                                class_suffix="column_filters"
                                                label=""
                                                :options="{{ $filters[$col['field']]['items'] }}"
                                                :selected="{{ $filters[$col['field']]['selected'] ?? $col['field'] }}"
                                                hx-include=".column_filters"
                                                hx-post="api/system/data_table_filter"
                                                hx-target=".data_table"
                                                hx-include=".column_filters"
                                                hx-swap="outerHTML"
                                        ></x-forms-select>
                                    @else
                                        {{$col['label'] }}
                                    @endif
                                </th>
                            @endforeach
                            <th scope="col" class="relative py-3.5 pr-4 pl-3 sm:pr-3">
                                <span class="sr-only">Edit</span>
                            </th>
                        </tr>
                        </thead>
@endif <!-- just_body -->
                        <tbody class="bg-white search_target data_table_body">
                        @if $columns['group_by']
                            @foreach ($data as $key => $group)
                            <tr class="border-t border-gray-200">
                                <th
                                        scope="colgroup"
                                        colspan="{{ count($columns) + 1 }}"
                                        class="bg-gray-50 py-2 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                    {{ $key }}
                                </th>
                            </tr>

                            @foreach ($group as $item)
                                <tr class="border-t {{ $loop->first ? 'border-gray-300' : 'border-gray-200' }}">
                                    <td class="
                                            {{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-3 py-4 text-sm text-gray-500 sm:table-cell' }}
                                        ">
                                        {{ $loop->first ? 'F' : $loop->iteration }}
                                    </td>
                                    @foreach ($columns as $col)
                                        <td class="
                                            {{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-3 py-4 text-sm text-gray-500 sm:table-cell' }}
                                        ">
                                             {{ $item[$col['field']] ?? '' }}
                                        </td>
                                    @endforeach
                                    <td class="relative py-4 pr-4 pl-3 text-right text-sm font-medium whitespace-nowrap sm:pr-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900">
                                            Edit<span class="sr-only">, {{ $item[$columns[0]['field']] ?? '' }}</span>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach

                        @endforeach
                        </tbody>
@if (!$just_body)
                    </table>
    </div></div>
@endif