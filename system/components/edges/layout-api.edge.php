@props([
    'title' => 'api view',
    'description' => 'right hand page of the layout',
    'view' => '',
    'function_call' => '',
    'namespace' => 'api\\' . str_replace('/', '\\', SOURCE_APP_PATH . '\\' . SOURCE_PAGE . '\\'),
    'params' => INPUT_PARAMS,
])

@php
    $view_exists = !empty($view) && file_exists($view);
    $api_error = false;
    $error_message = '';
@endphp

@if($view_exists)
    @include $view
@else
    @php
        if (!empty($view)) {
            $error_message = "API file not found: {$view}";
            $api_error = true;
            tcs_log($error_message, 'router_errors');
        }
    @endphp
@endif

@if($function_call)
  @php
            $path_parts = PATH_PARTS;
          if (count($path_parts)>2) {
              // Remove 'api' and 'system' from the start of path parts if they exist
              if ($path_parts[0] == 'api') {
                  array_shift($path_parts);
              }
              if ($path_parts[0] == 'system') {
                  array_shift($path_parts);
              }
              array_pop($path_parts); // Remove the function call from the path
              $namespace = 'api\\' . str_replace('/', '\\',  implode('\\', $path_parts) . '\\');
          }
          print_rr([
              'namespace' => $namespace,
              'function_call' => $function_call,
              'path_parts' => $path_parts
          ],'calling');
          $function_call = str_replace('\\\\', '\\', str_replace('\\\\', '\\', $namespace.$function_call));
          print_rr($function_call,'calling');

          if (function_exists($function_call)) {
              echo $function_call(INPUT_PARAMS);
          } else {
              $error_message = "API function not found: {$function_call}";
              $api_error = true;
              tcs_log($error_message,'router_errors');
              // Return JSON error for API calls
              header('Content-Type: application/json');
              echo json_encode(['error' => $error_message]);

              // Also trigger HTMX notification
              header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . addslashes($error_message) . '"}}');
          }

  @endphp
@endif

@if($api_error && !$function_call)
    @php
        // Return JSON error for API calls without function
        header('Content-Type: application/json');
        echo json_encode(['error' => $error_message]);

        // Also trigger HTMX notification
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . addslashes($error_message) . '"}}');
    @endphp
@endif
