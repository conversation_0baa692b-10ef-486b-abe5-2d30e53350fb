@props([
    'items' => [],
    'parent_path' => '',
    'depth' => 0,
 ]
)

<div x-data="{ currentRoute: '{{ trim(APP_PATH, "/") }}' }" x-init="
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        // Extract the path from the URL
        const url = new URL(event.detail.xhr.responseURL);
        const path = url.pathname.replace('{{ APP_ROOT }}', '').replace('get_view/', '');
        // Update the current route
        $data.currentRoute = path.replace(/^\/+|\/+$/g, '');
    });
">


<ul>
    @foreach($items as $key => $item)
        @if( isset($item['required_roles']) && !in_array(USER_ROLE, $item['required_roles']) ) @php continue; @endphp @endif
        <li :class='collapsed ? "mx-0" : "mx-0"'>
            @php
                $item_route = trim($parent_path . $key, '/');
            @endphp
            <a href='{{ $parent_path }}{{ $key }}'
               hx-get='{{ APP_ROOT }}get_view/{{ $parent_path }}{{ $key }}'
               hx-target='#content_wrapper'
               hx-replace-url='{{ APP_ROOT }}{{ $parent_path }}{{ $key }}' {{ $folder_out }}
               class='flex gap-x-3 p-2 text-sm font-semibold leading-6 group hover:text-white inset-shadow-sm'
               :class="currentRoute === '{{ $item_route }}' || (currentRoute.startsWith('{{ $item_route }}/') && {{ isset($item['sub_folder']) ? 'true' : 'false' }}) ? 'text-white bg-green-700' : 'text-indigo-200 bg-indigo-{{ $depth+6 }}00 hover:bg-indigo-{{ $depth + 6 + 1 }}00'"
               x-state:on='Current' x-state:off='Default'
               x-state="currentRoute === '{{ $item_route }}' || (currentRoute.startsWith('{{ $item_route }}/') && {{ isset($item['sub_folder']) ? 'true' : 'false' }}) ? 'on' : 'off'"
               @click="currentRoute = '{{ $item_route }}'"
               x-state-description='Current: &quot;border-y-2 border-y-green-600 text-white bg-green-700"&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'>
                <span class='shrink-0'>{{ ICONS[$item['icon']] }}</span>
                <span class='transition-opacity duration-300 whitespace-nowrap' :class='collapsed ? "opacity-0" : "opacity-100"'>
                    {{ $item['name'] }}
                </span>
            </a>
            @if(isset($item['sub_folder']))
                <div x-data="{ open: false }" x-effect="open = currentRoute.startsWith('{{ $item_route }}/')">
                    <div x-show="open" class="ml-4">
                        <x-nav-tree :items="$item['sub_folder']" :parent_path="$parent_path . $key . '/'" :depth="$depth + 1">
                    </div>
                </div>
            @endif
        </li>
    @endforeach

    @if(in_array(USER_ROLE, ['admin', 'dev']))
        <li :class='collapsed ? "mx-1" : "mx-2"' class="mt-2">
            <button
                hx-post="{{ APP_ROOT }}api/system/add_nav_entry"
                hx-vals='{"parent_path": "{{ $parent_path }}"}'
                hx-target="#modal_body"
                @click="$dispatch('show-modal')"
                class="flex gap-x-3 p-2 text-sm font-semibold leading-6 text-green-400 rounded-md group hover:text-white hover:bg-green-700 w-full"
            >
                <span class="shrink-0">{{ ICONS['plus'] }}</span>
                <span class="transition-opacity duration-300 whitespace-nowrap" :class='collapsed ? "opacity-0" : "opacity-100"'>
                    Add Entry
                </span>
            </button>
        </li>
    @endif
</ul>
</div>
