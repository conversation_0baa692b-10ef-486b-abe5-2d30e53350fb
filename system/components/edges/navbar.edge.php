@props([
    'title' => 'navbar',
    'description' => 'top navbar',
    'label' => '',
    'id' => 'navbar',
    'class_suffix' => '',
    'internal_class_suffix' => '',
])

<div class="h-16 sticky top-0 z-40 flex shrink-0 items-center gap-x-4 px-4 bg-white border-b border-gray-200 shadow-sm lg:px-8 sm:gap-x-6 sm:px-6">
    <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" @click="open = true">
        <span class="sr-only">Open sidebar</span>
        {{ icon('bars') }}
    </button>

    <!-- Separator -->
    <div class="bg-gray-900/10 w-px h-6 lg:hidden" aria-hidden="true"></div>

    <div class="flex flex-1 self-stretch gap-x-4 lg:gap-x-6">
        <form class="relative flex flex-1" action="#" method="GET">
            <label for="search-field" class="sr-only">Search</label>
            {{ icon('magnifying_glass', 'pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400') }}
            <input id="search-field"
                   class="w-full h-full block py-0 pr-0 pl-8 text-gray-900 border-0 focus:ring-0 placeholder:text-gray-400 sm:text-sm data_table_filter"
                   placeholder="Search..."
                   type="search" name="search_terms"
                   hx-trigger="input changed delay:500ms, search"
                   hx-include=".data_table_filter"
                   hx-post="api/system/data_table/data_table_filter"
                   hx-swap="outerhtml"
                   hx-target=".search_target"
            >
        </form>
        <div class="flex items-center gap-x-4 lg:gap-x-6">
            @if(in_array(users::checkAuth()['role'], ['dev']))
                <div class="flex items-center">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox"
                               name="debug_mode"
                               class="sr-only peer"
                               x-init="$el.checked='{{ $_SESSION['debug_mode'] ?? 0 }}'"
                               hx-post="{{ APP_ROOT }}api/system/toggle_debug_mode"
                               hx-trigger="change"
                               hx-swap="none"
                               hx-indicator="#debug-mode-indicator"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        <span class="ms-3 text-sm font-medium text-gray-900">Debug Mode</span>
                        <!-- Small loading indicator -->
                        <span id="debug-mode-indicator" class="htmx-indicator ml-2">
                            <svg class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                 fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </span>
                    </label>
                </div>
            @endif

            <div>

                @if (  file_exists(FS_APP_PATH . '/' . CURRENT_PAGE . '.settings.php')  )
                <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
                    hx-target="#content_wrapper"
                    hx-post="{{ APP_ROOT }}settings"
                    hx-replace-url="{{ '/'. tcs_path(APP_ROOT . APP_PATH .  '/' . CURRENT_PAGE . '/settings') }}"
                >
                    <span class="sr-only">View notifications</span>
                    @icon('cog')
                </button>
                @endif
            </div>
            <!-- Notification dropdown -->
            <x-notification-dropdown></x-notification-dropdown>
            <!-- User dropdown -->
            <div class="" x-data="{ open: false }">
                <div>
                    <button type="button"
                            class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
                            id="user-menu-button"
                            @click="open = !open"
                            @keydown.escape.window="open = false"
                            @click.away="open = false"
                            aria-expanded="false"
                            aria-haspopup="true">
                        @icon("user")
                        <span class="sr-only">Open user menu</span>

                    </button>
                </div>

                <div x-show="open"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                     role="menu"
                     aria-orientation="vertical"
                     aria-labelledby="user-menu-button"
                     tabindex="-1">
                    <a href="{{ APP_ROOT }}profile"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="{{ APP_ROOT }}profile"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Your Profile ({{ $_SESSION['username'] ?? users::checkAuth()['username'] }})
                    </a>

                    @if( in_array( users::checkAuth()['role'], ['admin', 'dev'] ) )
                        <div class="border-t border-gray-200 my-1"></div>
                        <a href="{{ APP_ROOT }}users"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                           role="menuitem"
                           tabindex="-1"
                           hx-get="{{ APP_ROOT }}users"
                           hx-target="#content_wrapper"
                           hx-replace-url="{{ APP_ROOT }}users"
                           hx-push-url="true">
                            User Management
                        </a>
                    @endif

                    <div class="border-t border-gray-200 my-1"></div>
                    <a href="{{ APP_ROOT }}settings"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-get="{{ APP_ROOT }}settings"
                       hx-target="#content_wrapper"
                       hx-push-url="true">
                        Settings
                    </a>
                    <a href="{{ APP_ROOT }}logout"
                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                       role="menuitem"
                       tabindex="-1"
                       hx-post="{{ APP_ROOT }}logout"
                       hx-target="body"
                       hx-push-url="true"
                       hx-redirect="{{ APP_ROOT }}login">
                        Sign out
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>