@props([
    'customers' => []
])

@if(count($customers) > 0)
    <div class="mt-2 bg-white shadow overflow-hidden rounded-md">
        <ul class="divide-y divide-gray-200">
            @foreach($customers as $customer)
                <li class="px-4 py-3 hover:bg-gray-50 cursor-pointer"
                    hx-post="api/quote-v3/select-customer"
                    hx-include="closest form"
                    hx-target="#customer-details"
                    hx-vals='{"csn": "{{ $customer['csn'] }}"}'>
                    <div class="flex justify-between">
                        <div class="text-sm font-medium text-indigo-600">{{ $customer['name'] }}</div>
                        <div class="text-sm text-gray-500">{{ $customer['csn'] }}</div>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>
@else
    <div class="mt-2 text-sm text-gray-500">
        No customers found. Please try a different search term.
    </div>
@endif
