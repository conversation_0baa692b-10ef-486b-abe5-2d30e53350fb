@props([
    'message' => 'Quote submitted successfully!',
    'quote_id' => null
])

<div class="rounded-md bg-green-50 p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">{{ $message }}</h3>
            @if($quote_id)
            <p class="mt-2 text-sm text-green-700">
                Your quote ID is: <span class="font-semibold">{{ $quote_id }}</span>
            </p>
            @endif
            <div class="mt-4">
                <div class="flex">
                    <x-forms-button
                        label="Create Another Quote"
                        variant="secondary"
                        type="button"
                        extra_attributes="hx-get='api/quote-v3/new' hx-target='#quote_form_container'"
                        class_suffix="bg-green-50 text-green-800 hover:bg-green-100 focus:ring-green-600 focus:ring-offset-green-50"
                    />
                    <x-forms-button
                        label="View All Quotes"
                        variant="secondary"
                        type="button"
                        extra_attributes="hx-get='quotes' hx-target='#main-content'"
                        class_suffix="ml-3 bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:ring-indigo-600"
                    />
                </div>
            </div>
        </div>
    </div>
</div>
