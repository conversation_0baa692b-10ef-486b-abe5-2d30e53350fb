@props([
    'title' => 'Template Container',
    'description' => 'Container for rendering selected templates',
    'template' => '', // The selected template name
    'template_data' => [], // Data to pass to the template
])

@php
// Check if the template exists
$template_path = "templates/{$template}";
$template_exists = file_exists(FS_APP_ROOT . "resources/components/templates/{$template}.edge.php");
@endphp

<div class="template-container">
    @if($template_exists)
        <x-dynamic-component :component="$template_path" :data="$template_data" />
    @else
        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div class="flex">
                <div class="flex-shrink-0">
                    @icon('exclamation-triangle', 'text-yellow-400')
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Template not found</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>The template "{{ $template }}" could not be found in the templates directory.</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
