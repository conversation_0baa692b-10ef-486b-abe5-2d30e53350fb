@props([
    'title' => 'Template Selector',
    'description' => 'Select a template from the templates directory',
    'name' => 'template',
    'label' => 'Select a template',
    'selected' => null, // Pre-selected template
    'class' => '',
    'extra_attributes' => '',
])

@php
// Get all template files from the templates directory
$template_dir = FS_APP_ROOT . 'resources/components/templates/';
$template_files = glob($template_dir . '*.edge.php');

// Format template options for the select
$options = [];
foreach ($template_files as $file) {
    $filename = basename($file, '.edge.php');
    $display_name = ucwords(str_replace('-', ' ', $filename));
    
    // Read the first few lines to extract title and description
    $content = file_get_contents($file);
    $title = $display_name;
    $description = '';
    
    if (preg_match("/'title' => '([^']+)'/", $content, $matches)) {
        $title = $matches[1];
    }
    
    if (preg_match("/'description' => '([^']+)'/", $content, $matches)) {
        $description = $matches[1];
    }
    
    $options[] = [
        'id' => $filename,
        'name' => $title,
        'description' => $description,
        'icon' => 'document-text'
    ];
}
@endphp

<div x-data='{
    open: false,
    hover: false,
    selectedOption: {{ json_encode($selected ? $selected : (count($options) > 0 ? $options[0] : null)) }},
    selectOption(option) {
        this.selectedOption = option;
        this.open = false;
        
        // Dispatch an event when a template is selected
        this.$dispatch("template-selected", {
            template: option.id,
            title: option.name,
            description: option.description
        });
    }
}' class="relative">
    @if($label)
        <label for="{{ $name }}" class="block text-sm/6 font-medium text-gray-900">{{ $label }}</label>
    @endif
    
    <div class="relative mt-2">
        <button type="button"
            @click="open = !open"
            @keydown.escape.window="open = false"
            @click.away="open = false"
            class="grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 {{ $class }}">
            <span class="col-start-1 row-start-1 flex items-center gap-3 pr-6">
                <span class="size-5 shrink-0 text-gray-400" x-html="selectedOption ? '@icon(' + (selectedOption.icon || 'document-text') + ')' : '@icon(\'question-mark-circle\')'">
                </span>
                <span class="block truncate" x-text="selectedOption ? selectedOption.name : 'Select a template'"></span>
            </span>
            <span class="col-start-1 row-start-1 self-center justify-self-end text-gray-500">
                @icon('chevron-up-down')
            </span>
        </button>

        <input type="hidden" name="{{ $name }}" x-bind:value="selectedOption ? selectedOption.id : ''">

        <div x-show="open" 
             x-transition:leave="transition ease-in duration-100"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden sm:text-sm">
            <ul class="">
                @foreach($options as $option)
                    <li @click='selectOption({{ json_encode($option) }})'
                        :class="[selectedOption && selectedOption.id === '{{ $option['id'] }}' ? 'bg-indigo-500 text-white' : '', 'relative cursor-default pt-1 pb-1 pr-9 pl-3 hover:bg-indigo-600 hover:text-white']">
                        <div class="flex items-center">
                            <span class="size-6 shrink-0 text-gray-400" :class="{'text-white': selectedOption && selectedOption.id === '{{ $option['id'] }}' && open}">
                                @icon($option['icon'] ?? 'document-text')
                            </span>
                            <span class="ml-3 block truncate">
                                {{ $option['name'] }}
                                <span class="block text-xs text-gray-500" :class="{'text-white': selectedOption && selectedOption.id === '{{ $option['id'] }}' && open}">
                                    {{ $option['description'] }}
                                </span>
                            </span>
                        </div>

                        <span x-show="selectedOption && selectedOption.id === '{{ $option['id'] }}'" 
                              :class="{'text-white': open && hover, 'text-indigo-600': !(open && hover)}"
                              class="absolute inset-y-0 right-0 flex items-center pr-4">
                            @icon('check')
                        </span>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
</div>
