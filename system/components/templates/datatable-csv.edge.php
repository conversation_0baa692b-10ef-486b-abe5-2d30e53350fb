@props([
    'title' => 'CSV Datatable',
    'description' => 'A datatable that displays data from a CSV file',
    'csv_data' => '', // CSV data as a string
    'has_header' => true, // Whether the CSV has a header row
    'delimiter' => ',', // CSV delimiter
    'id_count' => edge::id_count(),
    'class' => '', // Extra classes
    'items_per_page' => 30, // Max items to display before pagination
    'current_page_num' => 1,
])

@php
// Parse CSV data
$rows = [];
$headers = [];

if (!empty($csv_data)) {
    $lines = explode("\n", trim($csv_data));
    
    if (count($lines) > 0) {
        foreach ($lines as $index => $line) {
            if (empty(trim($line))) continue;
            
            $row = str_getcsv($line, $delimiter);
            
            if ($index === 0 && $has_header) {
                $headers = $row;
            } else {
                $rows[] = $row;
            }
        }
    }
}

// If no headers were found but we have rows, create numeric headers
if (empty($headers) && !empty($rows)) {
    $headers = array_map(function($i) { return "Column " . ($i + 1); }, range(0, count($rows[0]) - 1));
}

// Create columns for the datatable
$columns = [];
foreach ($headers as $index => $header) {
    $columns[] = [
        'label' => $header,
        'field' => $index,
        'filter' => false,
        'extra_parameters' => ''
    ];
}
@endphp

<div x-data="{
    csvData: '{{ addslashes($csv_data) }}',
    headers: {{ json_encode($headers) }},
    rows: {{ json_encode($rows) }},
    currentPage: {{ $current_page_num }},
    itemsPerPage: {{ $items_per_page }},
    
    get paginatedRows() {
        const start = (this.currentPage - 1) * this.itemsPerPage;
        return this.rows.slice(start, start + this.itemsPerPage);
    },
    
    get totalPages() {
        return Math.ceil(this.rows.length / this.itemsPerPage);
    }
}">
    @if(empty($headers) || empty($rows))
        <div class="text-center py-4 text-gray-500">
            No data available. Please upload a valid CSV file.
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="min-w-full border-collapse {{ $class }}">
                <thead>
                    <tr>
                        @foreach($headers as $header)
                            <th scope="col" class="relative sticky top-0 border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900">
                                {{ $header }}
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody class="bg-white">
                    <template x-for="(row, rowIndex) in paginatedRows" :key="rowIndex">
                        <tr class="border-t" :class="rowIndex === 0 ? 'border-gray-300' : 'border-gray-200'">
                            <template x-for="(cell, cellIndex) in row" :key="cellIndex">
                                <td class="whitespace-nowrap border-b border-gray-200 px-3 py-2 text-sm" :class="cellIndex === 0 ? 'font-medium text-gray-900' : 'text-gray-500'">
                                    <span x-text="cell"></span>
                                </td>
                            </template>
                        </tr>
                    </template>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="{{ count($headers) }}" class="px-3 py-2 border-t border-gray-300">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-700">
                                    Showing <span x-text="((currentPage - 1) * itemsPerPage) + 1"></span> to 
                                    <span x-text="Math.min(currentPage * itemsPerPage, rows.length)"></span> of 
                                    <span x-text="rows.length"></span> results
                                </div>
                                <div class="flex space-x-2">
                                    <button 
                                        @click="currentPage = Math.max(1, currentPage - 1)" 
                                        :disabled="currentPage === 1"
                                        class="px-3 py-1 rounded border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                        Previous
                                    </button>
                                    <button 
                                        @click="currentPage = Math.min(totalPages, currentPage + 1)" 
                                        :disabled="currentPage === totalPages"
                                        class="px-3 py-1 rounded border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    @endif
</div>
