

I want the table in the main content to have a sticky header but it's not working, why?

<!--<div class='debug'><pre> .
 **************************************

Array
(
    [fs_app_root] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/
    [domain] => www.cadservices.co.uk
    [script_name] => /baffletrain/autocadlt/autobooks/index.php
    [request_uri] => /baffletrain/autocadlt/autobooks/test/template
    [fs_doc_root] => /var/www/vhosts/cadservices.co.uk/httpdocs/
    [doc_root] => /var/www/vhosts/cadservices.co.uk/httpdocs/
    [app_fs] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/
    [app_root] => /baffletrain/autocadlt/autobooks/
    [set_by] => default
    [source_path] =>
    [source_page] =>
    [source_app_path] =>
    [app_path] => test/template
    [path_parts] => Array
        (
            [0] => test
            [1] => template
        )

    [top_level] => test
    [current_page] => template
    [fs_cache] => /var/www/vhosts/cadservices.co.uk/temp/autobooks
)
</pre><pre>
Function: print_rr, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/paths.php, Line: 55
Function: include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/startup_sequence.php, Line: 19
  Arguments:
   0: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/system\/paths.php"
Function: include_once, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 8
  Arguments:
   0: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/system\/startup_sequence.php"
</pre>
<br> **************************************
</div>	-->

<!--<div class='debug'> router.class.php>41: include/functions/template.fn.php not found
</div>	-->

<!doctype html>
<html class="h-full bg-white">
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<link rel="stylesheet" href="css/style.css">
<link rel="shortcut icon" href="img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<script defer src="https://unpkg.com/htmx.org@2.0.3"></script>
<script defer src="https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js"></script>
<link rel="stylesheet" href="/baffletrain/autocadlt/autobooks/ext/jodit/jodit.min.css" />
<script src="/baffletrain/autocadlt/autobooks/ext/jodit/jodit.min.js"></script>

<!-- layout-->
<body class="h-full" x-data="{showModal: false}" x-bind:class="showModal && 'overflow-hidden'">
<div class="bg-gray-100">
    <div x-data="sidebarState()" x-init="initialize()" @keydown.window.escape="open = false">
        <div class="hidden lg:w-56 lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:flex-col">
            <div

                    class="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:flex-col transition-all duration-300"
                    :class="collapsed ? 'w-11' : 'w-44'">
                <!-- Sidebar -->
                <div class="overflow-y-auto flex flex-col grow gap-y-5 px-0 pb-0 bg-indigo-600">
                    <div
                            @click="toggle()"
                            class="h-16 p-2 flex mx-0 space-y-1 shrink-0 items-center font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700 cursor-pointer">
                        <h2 class="flex">
                            <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                 class="size-6">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="m21 7.5-2.25-1.313M21 7.5v2.25m0-2.25-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3 2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75 2.25-1.313M12 21.75V19.5m0 2.25-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"></path>
                            </svg>
                            &nbsp;<span class="flex transition-opacity duration-300"
                                        :class="collapsed ? 'opacity-0' : 'opacity-100'">Autobooks</span>
                        </h2>
                    </div>
                    <nav class="flex flex-col flex-1">
                        <ul role="list" class="flex flex-col flex-1 gap-y-7">
                            <li>
                                <ul :class='collapsed ? "mx-0 space-y-1" : "mx-2 space-y-1"'
                                    class="transition-all duration-300">
                                    <ul class=''>
                                        <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                    href='/baffletrain/autocadlt/autobooks/dashboard'
                                                    hx-get='/baffletrain/autocadlt/autobooks/getview/dashboard'
                                                    hx-target='#content_wrapper'
                                                    hx-replace-url='/baffletrain/autocadlt/autobooks/dashboard'
                                                    class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                    x-state:on='Current' x-state:off='Default'
                                                    x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                        class='shrink-0'><svg xmlns="http://www.w3.org/2000/svg"
                                                                              fill="none" viewBox="0 0 24 24"
                                                                              stroke-width="1.5" stroke="currentColor"
                                                                              class="size-6"><path
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"/></svg></span>
                                                <span class='transition-opacity duration-300 whitespace-nowrap'
                                                      :class='collapsed ? "opacity-0" : "opacity-100"'>Dashboard</span></a>
                                        <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                    href='/baffletrain/autocadlt/autobooks/quotes'
                                                    hx-get='/baffletrain/autocadlt/autobooks/getview/quotes'
                                                    hx-target='#content_wrapper'
                                                    hx-replace-url='/baffletrain/autocadlt/autobooks/quotes'
                                                    class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                    x-state:on='Current' x-state:off='Default'
                                                    x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                        class='shrink-0'><svg xmlns="http://www.w3.org/2000/svg"
                                                                              fill="none" viewBox="0 0 24 24"
                                                                              stroke-width="1.5" stroke="currentColor"
                                                                              class="size-6"><path
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.068.157 2.148.279 3.238.364.466.037.893.281 1.153.671L12 21l2.652-3.978c.26-.39.687-.634 1.153-.67 1.09-.086 2.17-.208 3.238-.365 1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"/></svg></span>
                                                <span class='transition-opacity duration-300 whitespace-nowrap'
                                                      :class='collapsed ? "opacity-0" : "opacity-100"'>Quotes</span></a>
                                        <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                    href='/baffletrain/autocadlt/autobooks/subscriptions'
                                                    hx-get='/baffletrain/autocadlt/autobooks/getview/subscriptions'
                                                    hx-target='#content_wrapper'
                                                    hx-replace-url='/baffletrain/autocadlt/autobooks/subscriptions'
                                                    class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                    x-state:on='Current' x-state:off='Default'
                                                    x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                        class='shrink-0'><svg xmlns="http://www.w3.org/2000/svg"
                                                                              fill="none" viewBox="0 0 24 24"
                                                                              stroke-width="1.5" stroke="currentColor"
                                                                              class="size-6"><path
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                d="M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"/></svg></span>
                                                <span class='transition-opacity duration-300 whitespace-nowrap'
                                                      :class='collapsed ? "opacity-0" : "opacity-100"'>Subscriptions</span></a>
                                            <div x-data='{ open: true }'>
                                                <ul class=''>
                                                    <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                                href='/baffletrain/autocadlt/autobooks/subscriptions/email_history'
                                                                hx-get='/baffletrain/autocadlt/autobooks/getview/subscriptions/email_history'
                                                                hx-target='#content_wrapper'
                                                                hx-replace-url='/baffletrain/autocadlt/autobooks/subscriptions/email_history'
                                                                class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                                x-state:on='Current' x-state:off='Default'
                                                                x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                                    class='shrink-0'><svg
                                                                        xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                        viewBox="0 0 24 24" stroke-width="1.5"
                                                                        stroke="currentColor" class="size-6"><path
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round"
                                                                            d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"/></svg></span>
                                                            <span class='transition-opacity duration-300 whitespace-nowrap'
                                                                  :class='collapsed ? "opacity-0" : "opacity-100"'>Email History</span></a>
                                                </ul>
                                            </div>
                                        <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                    href='/baffletrain/autocadlt/autobooks/customers'
                                                    hx-get='/baffletrain/autocadlt/autobooks/getview/customers'
                                                    hx-target='#content_wrapper'
                                                    hx-replace-url='/baffletrain/autocadlt/autobooks/customers'
                                                    class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                    x-state:on='Current' x-state:off='Default'
                                                    x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                        class='shrink-0'><svg xmlns="http://www.w3.org/2000/svg"
                                                                              fill="none" viewBox="0 0 24 24"
                                                                              stroke-width="1.5" stroke="currentColor"
                                                                              class="size-6"><path
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"/></svg></span>
                                                <span class='transition-opacity duration-300 whitespace-nowrap'
                                                      :class='collapsed ? "opacity-0" : "opacity-100"'>Customers</span></a>
                                        <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                    href='/baffletrain/autocadlt/autobooks/products'
                                                    hx-get='/baffletrain/autocadlt/autobooks/getview/products'
                                                    hx-target='#content_wrapper'
                                                    hx-replace-url='/baffletrain/autocadlt/autobooks/products'
                                                    class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                    x-state:on='Current' x-state:off='Default'
                                                    x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                        class='shrink-0'><svg xmlns="http://www.w3.org/2000/svg"
                                                                              fill="none" viewBox="0 0 24 24"
                                                                              stroke-width="1.5" stroke="currentColor"
                                                                              class="size-6"><path
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"/></svg></span>
                                                <span class='transition-opacity duration-300 whitespace-nowrap'
                                                      :class='collapsed ? "opacity-0" : "opacity-100"'>Products</span></a>
                                        <li :class='collapsed ? \"mx-1\" : \"mx-2\"'><a
                                                    href='/baffletrain/autocadlt/autobooks/test'
                                                    hx-get='/baffletrain/autocadlt/autobooks/getview/test'
                                                    hx-target='#content_wrapper'
                                                    hx-replace-url='/baffletrain/autocadlt/autobooks/test'
                                                    class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700'
                                                    x-state:on='Current' x-state:off='Default'
                                                    x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'><span
                                                        class='shrink-0'><svg xmlns="http://www.w3.org/2000/svg"
                                                                              fill="none" viewBox="0 0 24 24"
                                                                              stroke-width="1.5" stroke="currentColor"
                                                                              class="size-6"><path
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                d="m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"/></svg></span>
                                                <span class='transition-opacity duration-300 whitespace-nowrap'
                                                      :class='collapsed ? "opacity-0" : "opacity-100"'>Test</span></a>
                                    </ul>
                                </ul>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>

        </div>
        <div class="transition-all duration-300" :class='collapsed ? "lg:pl-11" : "lg:pl-44"'>
            <div class="h-16 sticky top-0 z-40 flex shrink-0 items-center gap-x-4 px-4 bg-white border-b border-gray-200 shadow-sm lg:px-8 sm:gap-x-6 sm:px-6">
                <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" @click="open = true">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                         aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                    </svg>
                </button>

                <!-- Separator -->
                <div class="bg-gray-900/10 w-px h-6 lg:hidden" aria-hidden="true"></div>

                <div class="flex flex-1 self-stretch gap-x-4 lg:gap-x-6">
                    <form class="relative flex flex-1" action="#" method="GET">
                        <label for="search-field" class="sr-only">Search</label>
                        <svg class="w-5 h-full absolute inset-y-0 left-0 text-gray-400 pointer-events-none"
                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                                  clip-rule="evenodd"></path>
                        </svg>
                        <input id="search-field"
                               class="w-full h-full block py-0 pr-0 pl-8 text-gray-900 border-0 focus:ring-0 placeholder:text-gray-400 sm:text-sm"
                               placeholder="Search..." type="search" name="search_terms"
                               hx-trigger="input changed delay:500ms, search" hx-get="api/system/search"
                               hx-target=".search_target tbody"'>

                    </form>
                    <div class="flex items-center gap-x-4 lg:gap-x-6">
                        <div class='flex items-center relative'>
                            <button class='-m-2.5 p-2.5 text-gray-400 hover:text-gray-500' variant='no_border'
                                    hx-post='/baffletrain/autocadlt/autobooks/settings' hx-target='#content_wrapper'>
                                <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                     class="size-6">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z">
                                </svg>
                            </button>
                        </div>

                        <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
                            <span class="sr-only">View notifications</span>
                            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                 stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <main class="py-3 bg-gray-100 ">
                <div id="content_wrapper" class="px-4 bg-gray-100 lg:px-4 sm:px-4">


                </div>
            </main>
        </div>

        <!-- Footer -->


        <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-show="showModal">
            <div class="bg-opacity-75 fixed inset-0 bg-gray-500 transition-opacity" aria-hidden="true"
                 x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"></div>
            <div class="w-screen overflow-y-auto fixed inset-0 z-50">
                <div class="min-h-full flex justify-center items-end p-4 text-center sm:items-center sm:p-0">
                    <div class="transform overflow-hidden relative text-left bg-white rounded-lg shadow-xl transition-all sm:w-full sm:max-w-6xl sm:my-8"
                         @click.away="showModal = false" x-transition:enter="ease-out duration-300"
                         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100">
                        <div class="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
                            <div class="sm:flex sm:items-start">

                                <div id="modal_body" class="w-full mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

                                </div>
                            </div>
                        </div>
                        <div class="px-4 py-3 bg-gray-50 sm:flex sm:flex-row-reverse sm:px-6">
                            <button type="button"
                                    class="w-full inline-flex justify-center px-3 py-2 text-sm font-semibold text-white bg-red-600 rounded-md shadow-sm hover:bg-red-500 sm:w-auto sm:ml-3">
                                Deactivate
                            </button>
                            <button type="button"
                                    class="w-full inline-flex justify-center px-3 py-2 mt-3 text-sm font-semibold text-gray-900 bg-white rounded-md ring-1 ring-inset ring-gray-300 shadow-sm hover:bg-gray-50 sm:w-auto sm:mt-0">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</body>

</html>    </div>
<script>function sidebarState() {return {collapsed: false,initialize() {const cookieValue = document.cookie.split('; ').find(row => row.startsWith('sidebar-collapsed='))?.split('=')[1];this.collapsed = cookieValue === 'true';},toggle() {this.collapsed = !this.collapsed;document.cookie = `sidebar-collapsed=${this.collapsed}; path=/; max-age=31536000`;},};}</script>